@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

@theme {
  --font-sans: var(--font-geist-sans), ui-sans-serif, system-ui, sans-serif,
    "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
}

/* Additional theme enhancements */
@layer components {
  .theme-transition {
    transition: color 0.3s ease, background-color 0.3s ease,
      border-color 0.3s ease;
  }
}

@theme inline {
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
  /* Enhanced semantic colors */
  --color-success: var(--success);
  --color-success-foreground: var(--success-foreground);
  --color-error: var(--error);
  --color-error-foreground: var(--error-foreground);
  --color-warning: var(--warning);
  --color-warning-foreground: var(--warning-foreground);
  --color-info: var(--info);
  --color-info-foreground: var(--info-foreground);
  /* Color variations for StatCard component */
  --color-success-50: var(--success-50);
  --color-success-100: var(--success-100);
  --color-success-700: var(--success-700);
  --color-error-50: var(--error-50);
  --color-error-100: var(--error-100);
  --color-error-700: var(--error-700);
  --color-warning-50: var(--warning-50);
  --color-warning-100: var(--warning-100);
  --color-warning-700: var(--warning-700);
  --color-info-50: var(--info-50);
  --color-info-100: var(--info-100);
  --color-info-700: var(--info-700);
  --color-primary-50: var(--primary-50);
  --color-primary-200: var(--primary-200);
  --color-primary-700: var(--primary-700);
  /* Gradient support */
  --gradient-primary: var(--gradient-primary);
  --gradient-success: var(--gradient-success);
  --gradient-warning: var(--gradient-warning);
  --gradient-error: var(--gradient-error);
}

:root {
  --radius: 0.65rem;
  --background: oklch(1 0 0);
  --foreground: oklch(0.141 0.005 285.823);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.141 0.005 285.823);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.141 0.005 285.823);
  /* Enhanced primary colors with blue theme */
  --primary: oklch(0.623 0.214 259.815);
  --primary-foreground: oklch(0.97 0.014 254.604);
  --primary-50: oklch(0.95 0.05 259.815);
  --primary-200: oklch(0.85 0.12 259.815);
  --primary-700: oklch(0.50 0.25 259.815);
  --secondary: oklch(0.967 0.001 286.375);
  --secondary-foreground: oklch(0.21 0.006 285.885);
  --muted: oklch(0.967 0.001 286.375);
  --muted-foreground: oklch(0.552 0.016 285.938);
  --accent: oklch(0.967 0.001 286.375);
  --accent-foreground: oklch(0.21 0.006 285.885);
  --destructive: oklch(0.577 0.245 27.325);
  --border: oklch(0.92 0.004 286.32);
  --input: oklch(0.92 0.004 286.32);
  --ring: oklch(0.623 0.214 259.815);
  /* Enhanced semantic colors */
  --success: oklch(0.7 0.18 145);
  --success-foreground: oklch(0.15 0.05 145);
  --success-50: oklch(0.98 0.02 145);
  --success-100: oklch(0.95 0.08 145);
  --success-700: oklch(0.55 0.15 145);
  --error: oklch(0.65 0.25 15);
  --error-foreground: oklch(0.98 0.02 15);
  --error-50: oklch(0.98 0.02 15);
  --error-100: oklch(0.95 0.08 15);
  --error-700: oklch(0.5 0.22 15);
  --warning: oklch(0.78 0.2 45);
  --warning-foreground: oklch(0.15 0.05 45);
  --warning-50: oklch(0.98 0.02 45);
  --warning-100: oklch(0.95 0.08 45);
  --warning-700: oklch(0.6 0.18 45);
  --info: oklch(0.65 0.15 230);
  --info-foreground: oklch(0.98 0.02 230);
  --info-50: oklch(0.98 0.02 230);
  --info-100: oklch(0.95 0.08 230);
  --info-700: oklch(0.5 0.12 230);
  /* Enhanced chart colors with violet theme */
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  /* Gradient definitions */
  --gradient-primary: linear-gradient(
    135deg,
    oklch(0.623 0.214 259.815),
    oklch(0.50 0.25 259.815)
  );
  --gradient-success: linear-gradient(
    135deg,
    oklch(0.75 0.2 145),
    oklch(0.65 0.15 155)
  );
  --gradient-warning: linear-gradient(
    135deg,
    oklch(0.8 0.22 45),
    oklch(0.75 0.18 35)
  );
  --gradient-error: linear-gradient(
    135deg,
    oklch(0.7 0.27 15),
    oklch(0.6 0.22 25)
  );
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.141 0.005 285.823);
  --sidebar-primary: oklch(0.623 0.214 259.815);
  --sidebar-primary-foreground: oklch(0.97 0.014 254.604);
  --sidebar-accent: oklch(0.967 0.001 286.375);
  --sidebar-accent-foreground: oklch(0.21 0.006 285.885);
  --sidebar-border: oklch(0.92 0.004 286.32);
  --sidebar-ring: oklch(0.623 0.214 259.815);
}

.dark {
  --background: oklch(0.141 0.005 285.823);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.21 0.006 285.885);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.21 0.006 285.885);
  --popover-foreground: oklch(0.985 0 0);
  /* Enhanced primary colors for dark mode */
  --primary: oklch(0.546 0.245 262.881);
  --primary-foreground: oklch(0.379 0.146 265.522);
  --primary-50: oklch(0.2 0.02 262.881);
  --primary-200: oklch(0.3 0.08 262.881);
  --primary-700: oklch(0.75 0.2 262.881);
  --secondary: oklch(0.274 0.006 286.033);
  --secondary-foreground: oklch(0.985 0 0);
  --muted: oklch(0.274 0.006 286.033);
  --muted-foreground: oklch(0.705 0.015 286.067);
  --accent: oklch(0.274 0.006 286.033);
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.704 0.191 22.216);
  --border: oklch(1 0 0 / 10%);
  --input: oklch(1 0 0 / 15%);
  --ring: oklch(0.488 0.243 264.376);
  /* Enhanced semantic colors for dark mode */
  --success: oklch(0.75 0.2 145);
  --success-foreground: oklch(0.15 0.05 145);
  --success-50: oklch(0.2 0.02 145);
  --success-100: oklch(0.25 0.05 145);
  --success-700: oklch(0.65 0.18 145);
  --error: oklch(0.7 0.27 15);
  --error-foreground: oklch(0.98 0.02 15);
  --error-50: oklch(0.2 0.02 15);
  --error-100: oklch(0.25 0.05 15);
  --error-700: oklch(0.6 0.25 15);
  --warning: oklch(0.8 0.22 45);
  --warning-foreground: oklch(0.15 0.05 45);
  --warning-50: oklch(0.2 0.02 45);
  --warning-100: oklch(0.25 0.05 45);
  --warning-700: oklch(0.7 0.2 45);
  --info: oklch(0.7 0.18 230);
  --info-foreground: oklch(0.98 0.02 230);
  --info-50: oklch(0.2 0.02 230);
  --info-100: oklch(0.25 0.05 230);
  --info-700: oklch(0.6 0.15 230);
  /* Enhanced chart colors for dark mode */
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  /* Gradient definitions for dark mode */
  --gradient-primary: linear-gradient(
    135deg,
    oklch(0.546 0.245 262.881),
    oklch(0.45 0.25 262.881)
  );
  --gradient-success: linear-gradient(
    135deg,
    oklch(0.8 0.22 145),
    oklch(0.7 0.18 155)
  );
  --gradient-warning: linear-gradient(
    135deg,
    oklch(0.85 0.24 45),
    oklch(0.8 0.2 35)
  );
  --gradient-error: linear-gradient(
    135deg,
    oklch(0.75 0.29 15),
    oklch(0.65 0.24 25)
  );
  --sidebar: oklch(0.21 0.006 285.885);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.546 0.245 262.881);
  --sidebar-primary-foreground: oklch(0.379 0.146 265.522);
  --sidebar-accent: oklch(0.274 0.006 286.033);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(1 0 0 / 10%);
  --sidebar-ring: oklch(0.488 0.243 264.376);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Enhanced utility classes for the new color system */
@layer utilities {
  /* Gradient utilities */
  .bg-gradient-primary {
    background: var(--gradient-primary);
  }
  .bg-gradient-success {
    background: var(--gradient-success);
  }
  .bg-gradient-warning {
    background: var(--gradient-warning);
  }
  .bg-gradient-error {
    background: var(--gradient-error);
  }

  /* Enhanced hover effects */
  .hover-lift {
    transition: all 0.2s ease;
    &:hover {
      box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1),
        0 4px 6px -4px rgb(0 0 0 / 0.1);
      transform: translateY(-2px);
    }
  }

  /* Status indicators */
  .status-success {
    background-color: var(--success);
    color: var(--success-foreground);
  }
  .status-error {
    background-color: var(--error);
    color: var(--error-foreground);
  }
  .status-warning {
    background-color: var(--warning);
    color: var(--warning-foreground);
  }
  .status-info {
    background-color: var(--info);
    color: var(--info-foreground);
  }

  /* Debt status specific classes */
  .debt-overdue {
    background-color: var(--error-50);
    border-color: var(--error-100);
    color: var(--error-700);
  }
  .debt-current {
    background-color: var(--info-50);
    border-color: var(--info-100);
    color: var(--info-700);
  }
  .debt-paid {
    background-color: var(--success-50);
    border-color: var(--success-100);
    color: var(--success-700);
  }
  .debt-warning {
    background-color: var(--warning-50);
    border-color: var(--warning-100);
    color: var(--warning-700);
  }

  /* Interactive elements */
  .interactive-primary {
    background-color: var(--primary);
    color: var(--primary-foreground);
    transition: background-color 0.2s ease;
    &:hover {
      background-color: oklch(from var(--primary) l c h / 0.9);
    }
  }
  .interactive-success {
    background-color: var(--success);
    color: var(--success-foreground);
    transition: background-color 0.2s ease;
    &:hover {
      background-color: oklch(from var(--success) l c h / 0.9);
    }
  }
  .interactive-warning {
    background-color: var(--warning);
    color: var(--warning-foreground);
    transition: background-color 0.2s ease;
    &:hover {
      background-color: oklch(from var(--warning) l c h / 0.9);
    }
  }
  .interactive-error {
    background-color: var(--error);
    color: var(--error-foreground);
    transition: background-color 0.2s ease;
    &:hover {
      background-color: oklch(from var(--error) l c h / 0.9);
    }
  }

  /* Progress bars with gradients */
  .progress-primary {
    background: var(--gradient-primary);
    border-radius: 9999px;
  }
  .progress-success {
    background: var(--gradient-success);
    border-radius: 9999px;
  }
  .progress-warning {
    background: var(--gradient-warning);
    border-radius: 9999px;
  }
  .progress-error {
    background: var(--gradient-error);
    border-radius: 9999px;
  }
}
