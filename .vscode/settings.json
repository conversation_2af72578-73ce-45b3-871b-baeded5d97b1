{"[typescript]": {"editor.defaultFormatter": "dbaeumer.vscode-eslint"}, "deno.enablePaths": ["supabase/functions"], "deno.lint": true, "deno.unstable": ["bare-node-builtins", "byonm", "sloppy-imports", "unsafe-proto", "webgpu", "broadcast-channel", "worker-options", "cron", "kv", "ffi", "fs", "http", "net"], "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit", "source.organizeImports": "explicit"}, "editor.defaultFormatter": "esbenp.prettier-vscode", "editor.formatOnSave": true, "editor.inlayHints.enabled": "offUnlessPressed", "eslint.workingDirectories": ["./"], "files.autoSave": "onFocusChange", "search.exclude": {"**/.next": true, "**/build": true, "**/dist": true, "**/node_modules": true}, "typescript.inlayHints.functionLikeReturnTypes.enabled": true, "typescript.inlayHints.parameterNames.enabled": "all", "typescript.inlayHints.variableTypes.enabled": true, "typescript.preferences.importModuleSpecifier": "relative", "typescript.updateImportsOnFileMove.enabled": "always", "[typescriptreact]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "typescript.tsdk": "node_modules/typescript/lib"}