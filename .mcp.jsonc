{
	"mcpServers": {
		"filesystem": {
			"type": "stdio",
			"command": "npx",
			"args": ["@modelcontextprotocol/server-filesystem", "./"],
			"env": {}
		}
		// "language-server": {
		//   "command": "mcp-language-server",
		//   "args": [
		//     "--workspace",
		//     "/Users/<USER>/Projects/debt-tracker",
		//     "--lsp",
		//     "typescript-language-server",
		//     "--",
		//     "--stdio"
		//   ]
		// }
	}
}
