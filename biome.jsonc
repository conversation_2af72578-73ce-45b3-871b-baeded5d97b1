{"$schema": "./node_modules/@biomejs/biome/configuration_schema.json", "vcs": {"enabled": true, "clientKind": "git", "useIgnoreFile": true}, "files": {"ignoreUnknown": false, "includes": ["**", "!node_modules", "!dist", "!public", "!public/**", "!src/env.ts", "!.next"]}, "formatter": {"enabled": true}, "assist": {"actions": {"source": {"organizeImports": "on"}}}, "linter": {"enabled": true, "rules": {"nursery": {"useSortedClasses": {"level": "warn", "fix": "safe", "options": {"functions": ["clsx", "cva", "cn"]}}}, "recommended": true, "style": {"noParameterAssign": "error", "useAsConstAssertion": "error", "useDefaultParameterLast": "error", "useEnumInitializers": "error", "useSelfClosingElements": "error", "useSingleVarDeclarator": "error", "noUnusedTemplateLiteral": "error", "useNumberNamespace": "error", "noInferrableTypes": "error", "noUselessElse": "error"}}}}