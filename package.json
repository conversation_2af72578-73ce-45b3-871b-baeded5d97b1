{"name": "debt-tracker", "version": "0.1.0", "private": true, "type": "module", "scripts": {"build": "next build", "build:analyze": "ANALYZE=true next build", "build:profile": "next build --profile", "check": "biome check .", "check:unsafe": "biome check --write --unsafe .", "check:write": "biome check --write .", "dev": "next dev --turbo", "dev:profile": "NEXT_PUBLIC_PERFORMANCE_MONITORING=true next dev --turbo", "preview": "next build && next start", "start": "next start", "typecheck": "tsc --noEmit", "lighthouse": "npx lighthouse http://localhost:3000 --view", "format:check": "prettier --check \"**/*.{ts,tsx,js,jsx,mdx}\" --cache", "format:write": "prettier --write \"**/*.{ts,tsx,js,jsx,mdx}\" --cache", "lint": "next lint", "lint:fix": "next lint --fix"}, "dependencies": {"@clerk/elements": "^0.23.43", "@clerk/nextjs": "^6.25.4", "@headlessui/react": "^2.2.4", "@hookform/resolvers": "^5.1.1", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tooltip": "^1.2.7", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.52.0", "@t3-oss/env-nextjs": "^0.13.8", "@tanstack/react-query": "^5.83.0", "@tanstack/react-table": "^8.21.3", "@trpc/client": "^11.4.3", "@trpc/react-query": "^11.4.3", "@trpc/server": "^11.4.3", "babel-plugin-react-compiler": "^19.1.0-rc.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "currency.js": "^2.0.4", "date-fns": "^4.1.0", "eslint-plugin-react-hooks": "6.0.0-rc.1", "lucide-react": "^0.525.0", "motion": "^12.23.6", "next": "15.4.2-canary.7", "next-themes": "^0.4.6", "react": "^19.1.0", "react-day-picker": "^9.8.0", "react-dom": "^19.1.0", "react-hook-form": "^7.60.0", "recharts": "^3.1.0", "server-only": "^0.0.1", "sonner": "^2.0.6", "superjson": "^2.2.2", "tailwind-merge": "^3.3.1", "zod": "^4.0.5"}, "devDependencies": {"@biomejs/biome": "2.1.2", "@eslint/eslintrc": "^3.3.1", "@next/bundle-analyzer": "^15.4.1", "@tailwindcss/postcss": "^4.1.11", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@types/node": "^24.0.14", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@types/recharts": "^2.0.1", "eslint": "^9.31.0", "eslint-config-next": "^15.4.1", "eslint-plugin-import-x": "^4.16.1", "jest": "^30.0.4", "jest-environment-jsdom": "^30.0.4", "lefthook": "^1.12.2", "lighthouse": "^12.8.0", "postcss": "^8.5.6", "prettier": "^3.6.2", "prettier-plugin-tailwindcss": "^0.6.14", "tailwindcss": "^4.1.11", "tw-animate-css": "^1.3.5", "typescript": "^5.8.3", "typescript-eslint": "^8.37.0"}, "ct3aMetadata": {"initVersion": "7.39.3"}}